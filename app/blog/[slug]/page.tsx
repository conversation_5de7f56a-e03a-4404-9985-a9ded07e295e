'use client'

import React from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { ArrowLeft, Calendar, User, Clock, Share2, BookOpen, Tag } from 'lucide-react';

interface BlogDetailPageProps {
  params: {
    slug: string;
  };
}

const BlogDetailPage = ({ params }: BlogDetailPageProps) => {
  const { slug } = params;

  // Validate slug - in a real app, this would come from an API
  const validSlugs = ['how-we-built-alphax-ai-bot', 'ai-market-prediction-limitations', 'backtest-to-real-trade-validation', 'emotional-trading-vs-ai-bots'];

  if (!validSlugs.includes(slug)) {
    notFound();
  }

  // Mock blog post data - in a real app, this would come from an API
  const blogPost = {
    slug: 'how-we-built-alphax-ai-bot',
    title: 'How We Built AlphaX: Behind the AI That Beats the Market',
    excerpt: 'Discover how our data science team used Reinforcement Learning and market regime modeling to create AlphaX — our most followed and highest performing crypto bot.',
    content: `
      <p>Building AlphaX wasn't just about creating another trading bot—it was about revolutionizing how AI can understand and adapt to crypto market dynamics. Our journey began 18 months ago when we noticed that traditional technical analysis was failing to capture the unique volatility patterns in cryptocurrency markets.</p>

      <h2>The Challenge: Market Regime Detection</h2>
      <p>Cryptocurrency markets don't behave like traditional financial markets. They exhibit extreme volatility, sudden regime changes, and complex correlation patterns that shift rapidly. Our first breakthrough came when we developed a proprietary market regime detection algorithm that can identify:</p>
      
      <ul>
        <li><strong>Bull Market Momentum:</strong> Periods of sustained upward movement with specific volatility characteristics</li>
        <li><strong>Bear Market Corrections:</strong> Downward trends with distinct volume and price action patterns</li>
        <li><strong>Sideways Consolidation:</strong> Range-bound markets where mean-reversion strategies excel</li>
        <li><strong>High Volatility Breakouts:</strong> Explosive moves that require rapid position adjustments</li>
      </ul>

      <h2>Reinforcement Learning Architecture</h2>
      <p>At the core of AlphaX lies a sophisticated reinforcement learning system that continuously learns from market feedback. Unlike traditional rule-based systems, our RL agent:</p>

      <blockquote>
        <p>"The key insight was treating each trade not as an isolated decision, but as part of a continuous learning process where the AI adapts its strategy based on market feedback."</p>
        <cite>— Dr. Sarah Chen, Lead AI Researcher</cite>
      </blockquote>

      <p>Our RL system processes over 200 market features in real-time, including:</p>
      
      <ul>
        <li>Price action patterns across multiple timeframes</li>
        <li>Order book dynamics and market microstructure</li>
        <li>Social sentiment indicators from Twitter and Reddit</li>
        <li>On-chain metrics like whale movements and exchange flows</li>
        <li>Cross-asset correlations with traditional markets</li>
      </ul>

      <h2>Risk Management Integration</h2>
      <p>What sets AlphaX apart is its integrated risk management system. The AI doesn't just decide when to buy or sell—it dynamically adjusts position sizes, sets stop-losses, and manages portfolio exposure based on current market conditions.</p>

      <p>Our risk engine implements:</p>
      <ul>
        <li><strong>Dynamic Position Sizing:</strong> Larger positions during high-confidence setups, smaller during uncertainty</li>
        <li><strong>Adaptive Stop-Losses:</strong> Tighter stops in volatile markets, wider in trending conditions</li>
        <li><strong>Correlation-Based Hedging:</strong> Automatic hedging when portfolio correlation risk increases</li>
      </ul>

      <h2>Performance Validation</h2>
      <p>Before launching AlphaX to our users, we subjected it to rigorous backtesting across different market conditions:</p>

      <ul>
        <li><strong>Bull Market (2020-2021):</strong> +127% return vs +89% buy-and-hold</li>
        <li><strong>Bear Market (2022):</strong> -18% drawdown vs -68% market decline</li>
        <li><strong>Sideways Market (2023):</strong> +34% return vs -2% market performance</li>
      </ul>

      <h2>Continuous Improvement</h2>
      <p>AlphaX isn't a static system. Every trade provides new data that feeds back into our learning algorithms. We've implemented a continuous deployment pipeline that allows us to:</p>

      <ul>
        <li>Update model parameters based on recent performance</li>
        <li>Incorporate new market features as they become relevant</li>
        <li>Adjust risk parameters based on changing market volatility</li>
      </ul>

      <h2>What's Next?</h2>
      <p>We're currently working on AlphaX 2.0, which will include:</p>
      <ul>
        <li>Multi-asset portfolio optimization</li>
        <li>Integration with DeFi protocols for yield farming</li>
        <li>Advanced options strategies for volatility trading</li>
        <li>Cross-chain arbitrage opportunities</li>
      </ul>

      <p>The future of algorithmic trading lies not in rigid rules, but in adaptive AI systems that can learn and evolve with the markets. AlphaX represents just the beginning of this revolution.</p>
    `,
    image: 'https://images.pexels.com/photos/8369648/pexels-photo-8369648.jpeg?auto=compress&cs=tinysrgb&w=1200&h=600&fit=crop',
    author: 'Dr. Sarah Chen',
    authorBio: 'Lead AI Researcher at LightQuant with 10+ years in quantitative finance and machine learning.',
    authorAvatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop&crop=face',
    date: '2025-01-15',
    readTime: '8 min read',
    category: '🤖 AI Trading Strategies',
    tags: ['AI Trading', 'Machine Learning', 'AlphaX', 'Reinforcement Learning', 'Risk Management']
  };

  const relatedPosts = [
    {
      slug: 'ai-market-prediction-limitations',
      title: 'How AI Predicts Market Moves (And What It Gets Wrong)',
      image: 'https://images.pexels.com/photos/8369769/pexels-photo-8369769.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      date: '2025-01-08'
    },
    {
      slug: 'backtest-to-real-trade-validation',
      title: 'From Backtest to Real Trade: How We Validate Every Bot',
      image: 'https://images.pexels.com/photos/7567486/pexels-photo-7567486.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      date: '2025-01-10'
    },
    {
      slug: 'emotional-trading-vs-ai-bots',
      title: 'The Real Cost of Emotional Trading vs AI Bots',
      image: 'https://images.pexels.com/photos/7567443/pexels-photo-7567443.jpeg?auto=compress&cs=tinysrgb&w=400&h=250&fit=crop',
      date: '2025-01-05'
    }
  ];

  return (
      <article className="pt-20">
        {/* Hero Section */}
        <div className="relative h-96 bg-gradient-to-r from-slate-900 to-blue-900 overflow-hidden">
          <img 
            src={blogPost.image} 
            alt={blogPost.title}
            className="absolute inset-0 w-full h-full object-cover opacity-30"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-slate-900/80 to-blue-900/80"></div>
          
          <div className="relative z-10 max-w-4xl mx-auto px-6 h-full flex items-center">
            <div>
              <Link
                href="/blog"
                className="inline-flex items-center gap-2 text-blue-400 hover:text-blue-300 transition-colors mb-6"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Blog
              </Link>
              
              <div className="flex items-center gap-4 mb-4">
                <span className="px-3 py-1 bg-blue-600 text-white rounded-full text-sm font-medium">
                  {blogPost.category}
                </span>
                <span className="text-blue-200 text-sm">{blogPost.readTime}</span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                {blogPost.title}
              </h1>
              
              <div className="flex items-center gap-6 text-blue-200">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <span>{blogPost.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>{blogPost.date}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>{blogPost.readTime}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white">
          <div className="max-w-4xl mx-auto px-6 py-16">
            
            {/* Share Buttons */}
            <div className="flex items-center justify-between mb-12 pb-6 border-b border-slate-200">
              <div className="flex items-center gap-4">
                <span className="text-slate-600 font-medium">Share this article:</span>
                <div className="flex gap-2">
                  <button className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
                    <Share2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center gap-2 text-slate-500">
                <BookOpen className="w-4 h-4" />
                <span className="text-sm">{blogPost.readTime}</span>
              </div>
            </div>

            {/* Article Content */}
            <div 
              className="prose prose-lg max-w-none prose-headings:text-slate-900 prose-headings:font-bold prose-p:text-slate-700 prose-p:leading-relaxed prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-slate-900 prose-ul:text-slate-700 prose-blockquote:border-blue-500 prose-blockquote:bg-blue-50 prose-blockquote:p-6 prose-blockquote:rounded-xl"
              dangerouslySetInnerHTML={{ __html: blogPost.content }}
            />

            {/* Tags */}
            <div className="mt-12 pt-8 border-t border-slate-200">
              <div className="flex items-center gap-2 mb-4">
                <Tag className="w-5 h-5 text-slate-600" />
                <span className="font-medium text-slate-900">Tags:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {blogPost.tags.map((tag) => (
                  <span 
                    key={tag}
                    className="px-3 py-1 bg-slate-100 text-slate-600 rounded-full text-sm hover:bg-blue-100 hover:text-blue-600 transition-colors cursor-pointer"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Author Bio */}
            <div className="mt-12 p-8 bg-slate-50 rounded-2xl">
              <div className="flex items-start gap-4">
                <img 
                  src={blogPost.authorAvatar} 
                  alt={blogPost.author}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h3 className="text-xl font-bold text-slate-900 mb-2">About {blogPost.author}</h3>
                  <p className="text-slate-600 leading-relaxed">{blogPost.authorBio}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Posts */}
        <div className="bg-slate-50 py-16">
          <div className="max-w-7xl mx-auto px-6">
            <h2 className="text-3xl font-bold text-slate-900 mb-12 text-center">
              Related Articles
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              {relatedPosts.map((post, index) => (
                <Link
                  key={post.slug}
                  href={`/blog/${post.slug}`}
                  className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-slate-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-sm text-slate-500">{post.date}</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
          <div className="max-w-4xl mx-auto px-6 text-center text-white">
            <h2 className="text-3xl font-bold mb-4">
              🚀 Ready to Try AlphaX?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Experience the AI trading bot featured in this article with a 7-day free trial.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                Start Free Trial
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                View AlphaX Performance
              </button>
            </div>
          </div>
        </div>
      </article>
  );
};

export default function BlogPost({ params }: BlogDetailPageProps) {
  return <BlogDetailPage params={params} />;
}