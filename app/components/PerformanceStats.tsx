import React from 'react';
import { TrendingUp, Users, Link, TrendingDown } from 'lucide-react';

const PerformanceStats = () => {
  const stats = [
    {
      icon: TrendingUp,
      value: '+21.5%',
      label: 'avg. monthly ROI across all bots',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      icon: Users,
      value: '5,200+',
      label: 'active users',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      icon: Link,
      value: '3',
      label: 'major exchanges supported (Binance, Bybit, OKX)',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      icon: TrendingDown,
      value: '-32%',
      label: 'average drawdown vs market -47%',
      color: 'text-orange-500',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            📊 Our Numbers Speak for Themselves
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="group text-center p-8 rounded-3xl border border-slate-100 hover:border-slate-200 transition-all duration-500"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className={`w-16 h-16 ${stat.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bounce transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${stat.color}`} />
                </div>
                
                <div className={`text-4xl font-bold ${stat.color} mb-2 group-hover:scale-125 transition-transform duration-300`}>
                  {stat.value}
                </div>
                
                <p className="text-slate-600 text-sm leading-relaxed">
                  {stat.label}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default PerformanceStats;