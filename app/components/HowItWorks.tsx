import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';

const HowItWorks = () => {
  const steps = [
    {
      step: '1',
      icon: Link2,
      title: 'Connect Your Exchange',
      description: 'Link your Binance, Bybit, or OKX via secure API.',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      step: '2',
      icon: Bo<PERSON>,
      title: 'Choose a Bot',
      description: 'Filter bots by strategy, risk level, or performance.',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      step: '3',
      icon: Monitor,
      title: 'Monitor and Optimize',
      description: 'Let the bot trade. Track results live and adjust anytime.',
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            ⚙️ Get Started in 3 Simple Steps
          </h2>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div
                key={index}
                className={`group relative bg-white rounded-3xl p-8 border-2 ${step.borderColor} hover:shadow-2xl transition-all duration-500 transform`}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {/* Step Number */}
                <div className={`absolute -top-4 left-8 w-8 h-8 ${step.bgColor} ${step.color} rounded-full flex items-center justify-center font-bold text-lg border-2 ${step.borderColor} bg-white group-hover:animate-bounce`}>
                  {step.step}
                </div>
                
                <div className={`w-16 h-16 ${step.bgColor} rounded-2xl flex items-center justify-center mb-6 mt-4 group-hover:rotate-12 transition-transform duration-300`}>
                  <Icon className={`w-8 h-8 ${step.color}`} />
                </div>
                
                <h3 className="text-xl font-bold text-slate-900 mb-3">
                  {step.title}
                </h3>
                
                <p className="text-slate-600 leading-relaxed">
                  {step.description}
                </p>

                {/* Connector Arrow */}
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-9 transform -translate-y-1/2">
                    <div className="w-9 h-0.5 bg-slate-300"></div>
                    <div className="absolute -right-4 -top-1 w-0 h-0 border-l-4 border-l-slate-300 border-t-2 border-b-2 border-t-transparent border-b-transparent"></div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        <div className="text-center">
          <button className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105">
            👉 View Bot Marketplace
          </button>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;