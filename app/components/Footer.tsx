import React from 'react';
import { Twitter, MessageCircle, Send, Mail } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-slate-900 text-white py-16">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="md:col-span-1">
            <h3 className="text-2xl font-bold mb-4">
              Light<span className="text-blue-400">Quant</span>
            </h3>
            <p className="text-slate-400 leading-relaxed">
              A fully automated, smart trading platform that eliminates emotional bias and gives everyday users access to AI-driven quant tools.
            </p>
          </div>

          {/* Links */}
          <div className="md:col-span-2 grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-semibold mb-4 text-blue-400">Platform</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Chart</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Bot Marketplace</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4 text-blue-400">Legal</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Risk Disclosure</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API Documentation</a></li>
              </ul>
            </div>
          </div>

          {/* Contact & Social */}
          <div className="md:col-span-1">
            <h4 className="font-semibold mb-4 text-blue-400">Connect</h4>
            <div className="space-y-4">
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center gap-2 text-slate-400 hover:text-white transition-colors"
              >
                <Mail className="w-4 h-4" />
                <EMAIL>
              </a>
              
              <div className="flex gap-4">
                <a href="#" className="p-2 bg-slate-800 rounded-lg hover:bg-blue-600 transition-colors">
                  <Twitter className="w-5 h-5" />
                </a>
                <a href="#" className="p-2 bg-slate-800 rounded-lg hover:bg-purple-600 transition-colors">
                  <MessageCircle className="w-5 h-5" />
                </a>
                <a href="#" className="p-2 bg-slate-800 rounded-lg hover:bg-blue-500 transition-colors">
                  <Send className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-slate-400 text-sm">
            © 2025 LightQuant. All rights reserved.
          </p>
          <div className="flex gap-4 text-slate-400 text-sm">
            <span>🌐 Twitter</span>
            <span>💬 Discord</span>
            <span>📱 Telegram</span>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;