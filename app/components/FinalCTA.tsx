import React from 'react';
import { ArrowR<PERSON>, Sparkles } from 'lucide-react';

const FinalCTA = () => {
  const svgBackground = `data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E`;

  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div 
        className="absolute inset-0" 
        style={{ backgroundImage: `url("${svgBackground}")` }}
      ></div>
      
      <div className="relative z-10 max-w-4xl mx-auto px-6 text-center">
        <div className="mb-8">
          <Sparkles className="w-16 h-16 text-blue-400 mx-auto mb-6" />
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            🚀 Ready to Automate Your Trading?
          </h2>
          <p className="text-xl text-blue-100 mb-4 leading-relaxed">
            Get started today with a 7-day free trial.
          </p>
          <p className="text-lg text-slate-300 mb-12">
            No setup fees. No code. 100% results-driven.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="group bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400 text-white px-10 py-5 rounded-xl font-bold text-xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-green-500/25">
            <span className="flex items-center gap-3">
              Create Free Account
              <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
            </span>
          </button>
          
          <button className="border-2 border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white px-10 py-5 rounded-xl font-bold text-xl transition-all duration-300 transform hover:scale-105">
            Explore Bots
          </button>
        </div>
        {/* Trust Indicators */}
        <div className="mt-16 flex flex-wrap justify-center items-center gap-8 text-slate-400 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span>SSL Encrypted</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span>7-Day Free Trial</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
            <span>No Credit Card Required</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalCTA;