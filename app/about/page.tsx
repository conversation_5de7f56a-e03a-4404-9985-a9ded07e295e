import React from 'react';
import { ArrowRight, Users, Target, Brain, Rocket, Shield, TrendingUp, Globe, MessageCircle } from 'lucide-react';

const AboutPage = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      bio: 'A seasoned AI engineer and crypto trader with a background in algorithmic finance. Formerly at Goldman Sachs, <PERSON> founded LightQuant to bring hedge fund-grade strategies to the public.',
      avatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop&crop=face',
      gradient: 'from-blue-500 to-purple-500'
    },
    {
      name: '<PERSON>',
      role: 'Chief Technology Officer',
      bio: 'Ex-Google engineer with 10+ years building scalable cloud systems and trading infrastructure. Passionate about automation, reliability, and low-latency architecture.',
      avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop&crop=face',
      gradient: 'from-green-500 to-blue-500'
    },
    {
      name: 'Dr. <PERSON>',
      role: 'Head of Research & Machine Learning',
      bio: 'PhD in Quantitative Finance from MIT. Leads our bot development lab and ensures every strategy is scientifically validated with rigorous backtesting.',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&fit=crop&crop=face',
      gradient: 'from-purple-500 to-pink-500'
    }
  ];

  const focusAreas = [
    {
      icon: Brain,
      title: 'AI-Powered Strategies',
      description: 'Launching new machine learning-driven trading strategies monthly',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      icon: Shield,
      title: 'Smart Risk Controls',
      description: 'Enhancing portfolio diversification and risk management tools',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      icon: TrendingUp,
      title: 'Mobile Experience',
      description: 'Launching a mobile app with real-time bot insights and controls',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      icon: Users,
      title: 'Trading Community',
      description: 'Building a community of smart, data-driven investors',
      color: 'text-orange-500',
      bgColor: 'bg-orange-50'
    }
  ];

  const svgBackground = `data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234F46E5' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E`;

  return (
    <>
      {/* Hero Section */}
      <section className="relative pt-32 pb-16 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 overflow-hidden">
        <div 
          className="absolute inset-0 opacity-40" 
          style={{ backgroundImage: `url("${svgBackground}")` }}
        ></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in-up">
            About <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">LightQuant</span>
          </h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto animate-fade-in-up delay-200 text-balance">
            AI Crypto Bot Platform - Redefining automated trading through cutting-edge AI and machine learning
          </p>
        </div>
      </section>

      <div className="bg-slate-50">
        {/* Who We Are */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div className="animate-fade-in-up">
                <div className="flex items-center gap-3 mb-6">
                  <Rocket className="w-8 h-8 text-blue-600" />
                  <h2 className="text-4xl font-bold text-slate-900">Who We Are</h2>
                </div>
                <div className="space-y-6 text-lg text-slate-700 leading-relaxed">
                  <p>
                    We are a team of data scientists, engineers, and crypto enthusiasts on a mission to redefine automated trading through cutting-edge AI and machine learning. At the core of our platform is a belief that intelligent automation can unlock superior trading performance while reducing human error and emotional bias.
                  </p>
                  <p>
                    Since day one, our goal has been to make AI trading accessible, transparent, and profitable — not just for hedge funds or quant teams, but for every crypto investor.
                  </p>
                </div>
              </div>
              
              <div className="relative animate-fade-in-up delay-300">
                <div className="bg-white rounded-3xl p-8 shadow-2xl">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Brain className="w-8 h-8 text-blue-600" />
                      </div>
                      <h3 className="font-bold text-slate-900 mb-2">AI-Driven</h3>
                      <p className="text-slate-600 text-sm">Machine learning at the core</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Shield className="w-8 h-8 text-green-600" />
                      </div>
                      <h3 className="font-bold text-slate-900 mb-2">Transparent</h3>
                      <p className="text-slate-600 text-sm">Full performance history</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <TrendingUp className="w-8 h-8 text-purple-600" />
                      </div>
                      <h3 className="font-bold text-slate-900 mb-2">Profitable</h3>
                      <p className="text-slate-600 text-sm">Data-driven results</p>
                    </div>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Users className="w-8 h-8 text-orange-600" />
                      </div>
                      <h3 className="font-bold text-slate-900 mb-2">Accessible</h3>
                      <p className="text-slate-600 text-sm">For every investor</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* What We Do */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Brain className="w-8 h-8 text-blue-600" />
                <h2 className="text-4xl font-bold text-slate-900">What We Do</h2>
              </div>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                Our platform allows users to follow and deploy high-performance trading bots that operate across major crypto exchanges like Binance, Bybit, and OKX.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: '✅',
                  title: 'Trained on Real Market Data',
                  description: 'Every bot learns from actual market conditions and historical patterns'
                },
                {
                  icon: '✅',
                  title: 'Backtested & Risk-Assessed',
                  description: 'Rigorous testing and stress-testing across different market conditions'
                },
                {
                  icon: '✅',
                  title: 'Continuously Improved',
                  description: 'Machine learning algorithms that adapt and evolve with market changes'
                }
              ].map((feature, index) => (
                <div 
                  key={index}
                  className="group bg-slate-50 rounded-3xl p-8 hover:bg-white hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-slate-900 mb-3">{feature.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <p className="text-lg text-slate-600 max-w-2xl mx-auto text-balance">
                We handle the complex tech under the hood so you can focus on what matters — growing your portfolio.
              </p>
            </div>
          </div>
        </section>

        {/* Mission */}
        <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="max-w-7xl mx-auto px-6 text-center text-white">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Target className="w-8 h-8" />
              <h2 className="text-4xl font-bold">Our Mission</h2>
            </div>
            <p className="text-2xl mb-8 max-w-4xl mx-auto leading-relaxed">
              To empower crypto traders of all levels with AI-driven tools that maximize performance, minimize risk, and automate complex decision-making.
            </p>
            <p className="text-xl opacity-90 max-w-3xl mx-auto">
              We believe trading should be driven by data, not guesswork. That's why we invest heavily in research, infrastructure, and transparency — giving our users the edge they need in a highly volatile market.
            </p>
          </div>
        </section>

        {/* Leadership Team */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Users className="w-8 h-8 text-blue-600" />
                <h2 className="text-4xl font-bold text-slate-900">Leadership Team</h2>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <div 
                  key={index}
                  className="group bg-white rounded-3xl p-8 shadow-lg"
                >
                  <div className="text-center mb-6">
                    <div className="relative inline-block">
                      <img 
                        src={member.avatar} 
                        alt={member.name}
                        className="w-24 h-24 rounded-full object-cover mx-auto mb-4"
                      />
                    </div>
                    <h3 className="text-xl font-bold text-slate-900 mb-1">{member.name}</h3>
                    <p className={`font-medium bg-gradient-to-r ${member.gradient} bg-clip-text text-transparent`}>
                      {member.role}
                    </p>
                  </div>
                  <p className="text-slate-600 leading-relaxed text-center">
                    {member.bio}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Where We're Going */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-3 mb-6">
                <Globe className="w-8 h-8 text-blue-600" />
                <h2 className="text-4xl font-bold text-slate-900">Where We're Going</h2>
              </div>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto">
                We're building the future of fully autonomous trading, starting with crypto — and expanding to any asset class where algorithms can outperform.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-2xl font-bold text-slate-900 text-center mb-8">
                In 2025 and beyond, our focus areas include:
              </h3>
              
              <div className="grid md:grid-cols-2 gap-8">
                {focusAreas.map((area, index) => {
                  const Icon = area.icon;
                  return (
                    <div 
                      key={index}
                      className="group flex items-start gap-6 bg-slate-50 rounded-2xl p-6"
                    >
                      <div className={`w-12 h-12 ${area.bgColor} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className={`w-6 h-6 ${area.color}`} />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-slate-900 mb-2">{area.title}</h4>
                        <p className="text-slate-600">{area.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </section>

        {/* Join Us CTA */}
        <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
          <div className="max-w-7xl mx-auto px-6 text-center text-white">
            <div className="flex items-center justify-center gap-3 mb-6">
              <MessageCircle className="w-8 h-8" />
              <h2 className="text-4xl font-bold">Join Us</h2>
            </div>
            <p className="text-2xl mb-12 max-w-3xl mx-auto">
              Whether you're a beginner or an experienced trader, our platform was built to help you trade smarter, not harder.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="group bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25">
                <span className="flex items-center gap-2">
                  Explore Bots
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </span>
              </button>
              
              <button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-green-500/25">
                Start Free Trial
              </button>
              
              <button className="border-2 border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                Join Our Discord
              </button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default AboutPage;